// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:internet_connection_checker/internet_connection_checker.dart';
// import 'package:up_school_parent/src/core/utils/app_constants.dart';
// import 'package:up_school_parent/src/core/utils/notifications.dart';
//
// class AppConfig extends ChangeNotifier {
//   //? Web View Controller
//   InAppWebViewController? webViewController;
//
//   PullToRefreshController? pullToRefreshController;
//
//   //? Loading
//   bool isLoading = true;
//
//   set loading(bool value) {
//     isLoading = value;
//     notifyListeners();
//   }
//
//   //? Check Internet Connection
//   bool hasInternet = true;
//
//   void onWebViewCreated(InAppWebViewController controller) async {
//     webViewController = controller;
//
//     notifyListeners();
//   }
//
//   //? Initialize App
//   Future<void> init() async {
//     loading = true;
//
//     try {
//       await AppConfig.addFcmTokenToUrl();
//
//       await checkInternetConnection();
//
//       if (!hasInternet) {
//         isLoading = false;
//         notifyListeners();
//         return;
//       }
//
//       log('URL ${AppConstants.appUrl} fafasf $isLoading');
//
//       await initWebView();
//
//       loading = false;
//     } catch (e) {
//       log('Error $e');
//       loading = false;
//     }
//   }
//
//   //? Add User FCM to Url
//   static Future<void> addFcmTokenToUrl() async {
//     final token = await NotificationHandler.getToken();
//
//     AppConstants.appUrl += '?token=$token';
//   }
//
//   //? Check Internet Connection
//   Future<void> checkInternetConnection() async {
//     hasInternet = await InternetConnectionChecker().hasConnection;
//     notifyListeners();
//   }
//
//   //? Initialize Web View
//   Future<void> initWebView() async {
//     await webViewController?.loadUrl(
//         urlRequest: URLRequest(url: Uri.tryParse(AppConstants.appUrl)));
//
//     notifyListeners();
//   }
// }
//
// // class AppConfig extends ChangeNotifier {
// //   //? Web View Controller
// //   WebViewController controller = WebViewController();
// //
// //   //? Loading
// //   bool isLoading = true;
// //
// //   set loading(bool value) {
// //     isLoading = value;
// //     notifyListeners();
// //   }
// //
// //   //? Check Internet Connection
// //   bool hasInternet = true;
// //
// //   //? Initialize App
// //   Future<void> init() async {
// //     log('SSTTTEP 11');
// //     loading = true;
// //
// //     log('SSTTTEP 222');
// //
// //     await AppConfig.addFcmTokenToUrl();
// //
// //     log('SSTTTEP 333');
// //
// //     await checkInternetConnection();
// //
// //     log('SSTTTEP 4444');
// //
// //     if (!hasInternet) {
// //       isLoading = false;
// //       notifyListeners();
// //       return;
// //     }
// //
// //     log('URL ${AppConstants.appUrl}');
// //
// //     await initWebView();
// //   }
// //
// //   //? Add User FCM to Url
// //   static Future<void> addFcmTokenToUrl() async {
// //     final token = await NotificationHandler.getToken();
// //
// //     AppConstants.appUrl += '?token=$token';
// //   }
// //
// //   //? Check Internet Connection
// //   Future<void> checkInternetConnection() async {
// //     hasInternet = await InternetConnectionChecker().hasConnection;
// //     notifyListeners();
// //   }
// //
// //   //? Initialize Web View
// //   Future<void> initWebView() async {
// //     controller = WebViewController()
// //       ..setJavaScriptMode(JavaScriptMode.unrestricted)
// //       ..setBackgroundColor(const Color(0x00000000))
// //       ..setNavigationDelegate(
// //         NavigationDelegate(
// //           onPageFinished: (String url) {
// //             isLoading = false;
// //             notifyListeners();
// //           },
// //           onWebResourceError: (WebResourceError error) {
// //             log('Error ${error.description}');
// //           },
// //           onNavigationRequest: (NavigationRequest request) {
// //             return NavigationDecision.navigate;
// //           },
// //         ),
// //       )
// //       ..loadRequest(Uri.parse(AppConstants.appUrl));
// //
// //     notifyListeners();
// //   }
// // }
