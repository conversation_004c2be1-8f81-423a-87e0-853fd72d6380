// import 'dart:developer';
//
// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:up_school_parent/firebase_options.dart';
//
// class NotificationHandler {
//   static Future<void> _firebaseMessagingBackgroundHandler(
//       RemoteMessage message) async {
//     if (Firebase.apps.isEmpty) {
//       await Firebase.initializeApp(
//           options: DefaultFirebaseOptions.currentPlatform);
//     }
//   }
//
//   //! Request Notification Permissions & Init
//   static Future<void> initNotifications() async {
//     final fcm = FirebaseMessaging.instance;
//
//     await fcm.requestPermission(
//       alert: true,
//       badge: true,
//       provisional: false,
//       sound: true,
//     );
//
//     await fcm.setForegroundNotificationPresentationOptions(
//         badge: true, alert: true, sound: true);
//
//     FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
//   }
//
//   //! Get User FCM Token
//   static Future<String?> getToken() async {
//     final fcm = FirebaseMessaging.instance;
//     final fcmToken = await fcm.getToken();
//
//     log('Token $fcmToken');
//
//     return fcmToken;
//   }
// }
