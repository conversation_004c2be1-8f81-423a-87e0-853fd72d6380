import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';

class OneSignalNotificationService {
  static Future<void> initOneSignal() async {
    try {
      OneSignal.Debug.setLogLevel(
        kDebugMode ? OSLogLevel.verbose : OSLogLevel.none,
      );

      OneSignal.initialize(AppConstants.oneSignalAppId);

      //? check if has permission
      final canRequestPermission = await OneSignal.Notifications.canRequest();

      if (canRequestPermission) {
        await OneSignal.Notifications.requestPermission(true);
      }
    } on Exception catch (e) {
      log('OneSignalError $e');
    }
  }

  //? Get User Id
  static String getUserId() {
    final id = OneSignal.User.pushSubscription.id;
    return id ?? '';
  }

  //? Handle if new notification received
  static void handleNotificationReceived(OSNotification notification) {
    log('Notification Received ${notification.jsonRepresentation()}');
  }
}
