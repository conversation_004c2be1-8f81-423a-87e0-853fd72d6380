import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:provider/provider.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';
import 'package:up_school_parent/src/pages/services/download_methods.dart';
import 'package:up_school_parent/src/pages/widgets/no_internet_connection_widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../core/config/app_config.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? webViewController;

  @override
  void initState() {
    super.initState();
    // Force reload when the widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _forceReload();
    });
  }

  // Force reload the WebView to get latest content
  Future<void> _forceReload() async {
    if (webViewController != null) {
      await webViewController!.reload();
    }
  }

  @override
  Widget build(BuildContext context) {
    void onDownloadStart(controller, request) async {
      Log.i("onDownloadStartSSSS $request");

      final data = await controller.evaluateJavascript(
          source: "window.localStorage.getItem('UPSTlink')");

      Log.i("onDownloadStartSSSS444 $data");

      try {
        await prepareSaveDir();
        {
          final lang = await controller.evaluateJavascript(
              source: "window.localStorage.getItem('UPSTlang')");

          final isArabic = lang.toString().contains("ar");

          context
              .showBarMessage(isArabic ? "...جار ي الفتح" : "פותח את הקובץ...");

          downloadFiles(context, controller: controller);
        }
      } catch (e) {
        Log.e('DownloadError: $e');
      }
    }

    return Scaffold(body: SafeArea(
      child: Consumer<AppConfig>(
        builder: (context, appConfig, child) {
          if (!appConfig.hasInternet) {
            return const NoInternetConnectionWidget();
          }

          return InAppWebView(
            initialSettings: InAppWebViewSettings(
              cacheEnabled: false,
              clearCache: true,
              domStorageEnabled:
                  true, // Ensure localStorage/sessionStorage work
            ),
            onWebViewCreated: (controller) async {
              webViewController = controller;
              await appConfig.onWebViewCreated(controller);

              // Configure cache settings to disable caching but preserve sessions
              await controller.setSettings(
                  settings: InAppWebViewSettings(
                cacheEnabled: false,
                clearCache: true,
                domStorageEnabled: true,
              ));

              // Force reload to get latest content
              await _forceReload();
            },
            onLoadStop: (controller, url) async {
              await appConfig.addTokenToLogin(controller: controller);
            },
            onProgressChanged: (controller, progress) async {
              if (progress == 100) {
                await appConfig.addTokenToLogin(controller: controller);
              }
            },
            onUpdateVisitedHistory: (controller, url, androidIsReload) async {
              await appConfig.addTokenToLogin(controller: controller);
            },
            onDownloadStartRequest: onDownloadStart,
            onReceivedServerTrustAuthRequest: (controller, challenge) async {
              return ServerTrustAuthResponse(
                  action: ServerTrustAuthResponseAction.PROCEED);
            },
            initialUrlRequest: URLRequest(
              url: WebUri.uri(
                Uri.parse(
                    "${AppConstants.appUrl}?v=${DateTime.now().millisecondsSinceEpoch}"),
              ),
            ),
          );
        },
      ),
    ));
  }
}
