import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:up_school_parent/src/core/config/app_config.dart';
import 'package:up_school_parent/src/core/utils/app_constants.dart';

class NoInternetConnectionWidget extends StatelessWidget {
  const NoInternetConnectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Center(
          child: Lottie.asset(
            AppConstants.noInternetPath,
            height: 200,
          ),
        ),

        const SizedBox(height: 40),

        Text(
          "No Internet Connection !",
          style: Theme.of(context).textTheme.headlineMedium,
        ),

        const SizedBox(height: 40),

        // try again Button

        ElevatedButton(
          style: ElevatedButton.styleFrom(
            // primary: Theme.of(context).primaryColor,
            // onPrimary: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
          ),
          onPressed: () {
            context.read<AppConfig>().init();
          },
          child: const Text("Try Again"),
        )
      ],
    );
  }
}
