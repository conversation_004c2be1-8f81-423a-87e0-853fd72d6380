PODS:
  - Firebase/CoreOnly (10.12.0):
    - FirebaseCore (= 10.12.0)
  - Firebase/Messaging (10.12.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.12.0)
  - firebase_core (2.15.0):
    - Firebase/CoreOnly (~> 10.12.0)
    - FlutterMacOS
  - firebase_messaging (14.6.5):
    - Firebase/CoreOnly (~> 10.12.0)
    - Firebase/Messaging (~> 10.12.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseCore (10.12.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.15.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.15.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.12.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FlutterMacOS (1.0.0)
  - GoogleDataTransport (9.2.5):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.11.5):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.5):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.5):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.5):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.5)"
  - GoogleUtilities/Reachability (7.11.5):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.11.5):
    - GoogleUtilities/Logger
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - PromisesObjC (2.3.1)

DEPENDENCIES:
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  FlutterMacOS:
    :path: Flutter/ephemeral

SPEC CHECKSUMS:
  Firebase: 07150e75d142fb9399f6777fa56a187b17f833a0
  firebase_core: ff59797157ca9adda4440071643761b41fcd03b3
  firebase_messaging: d489df2f5cf5eb4b1ffb0b920f1d8c6b911f6166
  FirebaseCore: f86a1394906b97ac445ae49c92552a9425831bed
  FirebaseCoreInternal: 2f4bee5ed00301b5e56da0849268797a2dd31fb4
  FirebaseInstallations: cae95cab0f965ce05b805189de1d4c70b11c76fb
  FirebaseMessaging: bb2c4f6422a753038fe137d90ae7c1af57251316
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  GoogleDataTransport: 54dee9d48d14580407f8f5fbf2f496e92437a2f2
  GoogleUtilities: 13e2c67ede716b8741c7989e26893d151b2b2084
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.12.1
