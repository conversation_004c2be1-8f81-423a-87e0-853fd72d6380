part of xr_helper;

extension ColumnExtensions on List<Widget> {
  // * Column ==================================
  Widget column(
      {MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
      CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
      MainAxisSize mainAxisSize = MainAxisSize.max}) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: this,
    );
  }

  // * Row ==================================
  Widget row(
      {MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
      CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
      MainAxisSize mainAxisSize = MainAxisSize.max}) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: this,
    );
  }

  // * Wrap ==================================
  Widget wrap(
      {Axis direction = Axis.horizontal,
      WrapAlignment alignment = WrapAlignment.start,
      WrapCrossAlignment crossAxisAlignment = WrapCrossAlignment.start,
      double spacing = 0,
      double runSpacing = 0,
      WrapAlignment runAlignment = WrapAlignment.start,
      WrapCrossAlignment runCrossAxisAlignment = WrapCrossAlignment.start,
      VerticalDirection verticalDirection = VerticalDirection.down,
      Clip clipBehavior = Clip.none}) {
    return Wrap(
      direction: direction,
      alignment: alignment,
      crossAxisAlignment: crossAxisAlignment,
      spacing: spacing,
      runSpacing: runSpacing,
      runAlignment: runAlignment,
      verticalDirection: verticalDirection,
      clipBehavior: clipBehavior,
      children: this,
    );
  }
}
