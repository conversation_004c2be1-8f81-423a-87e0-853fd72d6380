part of xr_helper;

class BaseCachedImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double? radius;

  const BaseCachedImage(
    this.imageUrl, {
    super.key,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    const errorWidget = CircleAvatar(
      backgroundColor: Colors.grey,
      radius: 30,
      child: Icon(
        Icons.error,
        color: Colors.white,
      ),
    );

    return CachedNetworkImage(
        imageUrl: imageUrl,
        fadeInDuration: const Duration(milliseconds: 400),
        height: height,
        width: width,
        fit: fit,
        progressIndicatorBuilder: (context, url, progress) =>
            loadingShimmerWidget(),
        errorWidget: (context, url, error) => errorWidget).sized(
      height: height,
      width: width,
    );
  }

  Widget loadingShimmerWidget() => Center(
        child: Shimmer(
          gradient: LinearGradient(
            colors: [
              Colors.grey[300]!,
              Colors.grey[100]!,
            ],
          ),
          child: Container(
              height: height,
              width: width,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: radius == null
                    ? null
                    : BorderRadius.all(Radius.circular(radius!)),
              )),
        ),
      );
}
