part of xr_helper;

class BaseDatePicker extends HookWidget {
  final ValueNotifier<DateTime?> selectedDateNotifier;
  final String label;
  final void Function(DateTime?)? onChanged;
  final bool isRequired;
  final bool isWhite;

  const BaseDatePicker({
    super.key,
    this.onChanged,
    required this.selectedDateNotifier,
    required this.label,
    this.isWhite = false,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget errorText(String? errorText) {
      if (errorText == null) {
        return const SizedBox.shrink();
      }
      return Padding(
        padding: const EdgeInsets.only(left: 12, bottom: 8.0),
        child: Text(
          errorText,
          style: TextStyle(
            color: Theme
                .of(context)
                .colorScheme
                .error,
          ),
        ),
      );
    }

    return ValueListenableBuilder(
      valueListenable: selectedDateNotifier,
      builder: (context, value, child) {
        final emptyDate = value == null;

        final selectedDate = value;

        return FormField(

          validator: (value) {
            if (isRequired && emptyDate) {
              return "Please select $label";
            }
            return null;
          },
          builder: (state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      locale: Localizations.localeOf(context),
                      context: context,
                      initialDate: selectedDate == null ||
                          selectedDate.isBefore(DateTime(2000))
                          ? DateTime.now()
                          : selectedDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                      builder: (BuildContext context, Widget? child) {
                        return child!;
                      },
                    );
                    if (date == null) return;
                    selectedDateNotifier.value = date;

                    if (onChanged != null) onChanged!(date);
                    state.didChange(date);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        const CircleAvatar(

                          backgroundColor: AppColors.secondaryColor,
                          child: Icon(
                            Icons.calendar_today,
                          ),
                        ),
                        context.smallGap,
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: FittedBox(
                                      fit: BoxFit.scaleDown,
                                      child: Text(
                                        emptyDate
                                            ? "Select $label"
                                            : DateFormat('yyyy-MM-dd', 'en')
                                            .format(selectedDate!),
                                        style: emptyDate
                                            ? context.hint.copyWith(
                                          color: isWhite
                                              ? Colors.white
                                              : Colors.black,
                                        )
                                            : context.subTitle.copyWith(
                                          color: isWhite
                                              ? Colors.white
                                              : Colors.black,
                                        ),
                                        maxLines: 1,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                errorText(state.errorText),
              ],
            ).decorated(
              height: 60,
              radius: BorderRadius.circular(AppRadius.smallRadius),
              border: Border.all(
                color: state.hasError
                    ? Theme
                    .of(context)
                    .colorScheme
                    .error
                    : AppColors.secondaryColor,
              ),
            );
          },
        );
      },
    );
  }
}
