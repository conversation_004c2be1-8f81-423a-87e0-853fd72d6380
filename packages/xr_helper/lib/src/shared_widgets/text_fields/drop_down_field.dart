part of xr_helper;

class BaseDropDown extends StatelessWidget {
  final dynamic selectedValue;
  final String? label;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final String Function(dynamic)? asString;
  final bool isRequired;
  final bool showTitle;
  final bool isWhiteText;

  const BaseDropDown({super.key,
    required this.onChanged,
    this.asString,
    required this.data,
    required this.label,
    required this.selectedValue,
    this.isRequired = true,
    this.showTitle = true,
    this.isWhiteText = false,
    this.icon});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DropdownButtonFormField(
          value: selectedValue,
          isExpanded: true,
          borderRadius: BorderRadius.circular(AppRadius.smallRadius),
          items: data.map((e) {
            return DropdownMenuItem(
              value: e,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Text(
                    asString != null ? asString!(e) : e.toString(),
                    style:isWhiteText? context.whiteLabelLarge: context.labelLarge,
                  ),
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(
              vertical: AppSpaces.mediumPadding,
              horizontal: icon == null
                  ? AppSpaces.mediumPadding
                  : AppSpaces.smallPadding,
            ),
            label: Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.smallPadding),
              child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(label!, style: isWhiteText ?context.whiteLabelMedium: context.labelMedium)),
            ),
            labelStyle:  TextStyle(color:isWhiteText ? Colors.white: Colors.white),
            border: InputBorder.none,
            fillColor: Colors.transparent,
            prefixIcon: icon == null ? null : Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: 20),
              child: icon,
            ),
          ),
          validator: (value) {
            if (value == null) {
              return 'Please select $label';
            }
            return null;
          },
        ),
      ],
    );
  }
}
